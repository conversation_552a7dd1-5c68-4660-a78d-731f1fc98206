#!/usr/bin/env python3
"""
Test script to debug the save_interrupt_audio function
"""

import wave
import os

def save_interrupt_audio(audio_data: bytes, session_id: str, sample_rate: int = 16000):
    """
    Test version of save_interrupt_audio function
    """
    print(f"[DEBUG] save_interrupt_audio called with:")
    print(f"  - audio_data type: {type(audio_data)}")
    print(f"  - audio_data length: {len(audio_data) if audio_data else 0} bytes")
    print(f"  - session_id: {session_id}")
    print(f"  - sample_rate: {sample_rate}")
    
    if not audio_data:
        print("[DEBUG] No audio data provided, returning early")
        return
    
    # Save in project root as 'interrupt_last_recorded.wav' (overwrite each time)
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
    filename = 'interrupt_last_recorded.wav'
    filepath = os.path.join(project_root, filename)
    
    print(f"[DEBUG] Saving to: {filepath}")
    
    try:
        with wave.open(filepath, 'wb') as wf:
            wf.setnchannels(1)        # mono
            wf.setsampwidth(2)        # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(audio_data)
        
        print(f"[SUCCESS] Audio saved to {filepath}")
        
        # Verify file was created
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"[SUCCESS] File exists with size: {file_size} bytes")
        else:
            print("[ERROR] File was not created")
            
        return filepath
        
    except Exception as e:
        print(f"[ERROR] Failed to save audio: {e}")
        import traceback
        traceback.print_exc()
        raise

def test_with_sample_data():
    """
    Test the function with sample audio data
    """
    print("=== Testing save_interrupt_audio with sample data ===")
    
    # Create sample 16-bit audio data (1 second of sine wave)
    import math
    sample_rate = 16000
    duration = 1.0  # 1 second
    frequency = 440  # A4 note
    
    samples = []
    for i in range(int(sample_rate * duration)):
        t = i / sample_rate
        sample = int(32767 * 0.5 * math.sin(2 * math.pi * frequency * t))
        # Convert to 16-bit signed integer bytes (little endian)
        samples.append(sample.to_bytes(2, byteorder='little', signed=True))
    
    audio_data = b''.join(samples)
    
    print(f"Generated {len(audio_data)} bytes of audio data")
    
    # Test the function
    result = save_interrupt_audio(audio_data, 'test_session_123', sample_rate)
    return result

def test_with_empty_data():
    """
    Test the function with empty data
    """
    print("\n=== Testing save_interrupt_audio with empty data ===")
    result = save_interrupt_audio(b'', 'test_session_empty')
    print(f"Result with empty data: {result}")

def test_with_none_data():
    """
    Test the function with None data
    """
    print("\n=== Testing save_interrupt_audio with None data ===")
    result = save_interrupt_audio(None, 'test_session_none')
    print(f"Result with None data: {result}")

if __name__ == "__main__":
    try:
        # Test with sample data
        test_with_sample_data()
        
        # Test edge cases
        test_with_empty_data()
        test_with_none_data()
        
        print("\n=== All tests completed ===")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
