#!/usr/bin/env python3
"""
Test script to verify the fixed save_interrupt_audio function works correctly
"""

import sys
import os
import math

# Add project root to path
sys.path.append('.')

def test_audio_save_function():
    """Test the save_interrupt_audio function directly"""
    try:
        # Import the function
        from core.interruption.interrupt_manager import save_interrupt_audio
        
        print("✅ Successfully imported save_interrupt_audio function")
        
        # Create sample 16-bit audio data (1 second of sine wave)
        sample_rate = 16000
        duration = 1.0  # 1 second
        frequency = 440  # A4 note
        
        samples = []
        for i in range(int(sample_rate * duration)):
            t = i / sample_rate
            sample = int(32767 * 0.5 * math.sin(2 * math.pi * frequency * t))
            # Convert to 16-bit signed integer bytes (little endian)
            samples.append(sample.to_bytes(2, byteorder='little', signed=True))
        
        audio_data = b''.join(samples)
        print(f"✅ Generated {len(audio_data)} bytes of test audio data")
        
        # Test the function
        result = save_interrupt_audio(audio_data, 'test_session_fixed', sample_rate)
        print(f"📁 Function returned: {result}")
        
        if result:
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✅ SUCCESS: Audio file created at {result} with size {file_size} bytes")
                return True
            else:
                print(f"❌ ERROR: Function returned path {result} but file does not exist")
                return False
        else:
            print("❌ ERROR: Function returned None")
            return False
            
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        print("This indicates a dependency issue preventing the import")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_audio_data():
    """Test with empty audio data"""
    try:
        from core.interruption.interrupt_manager import save_interrupt_audio
        
        print("\n🧪 Testing with empty audio data...")
        result = save_interrupt_audio(b'', 'test_session_empty')
        print(f"📁 Result with empty data: {result}")
        
        if result is None:
            print("✅ SUCCESS: Function correctly returned None for empty data")
            return True
        else:
            print("❌ ERROR: Function should return None for empty data")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_none_audio_data():
    """Test with None audio data"""
    try:
        from core.interruption.interrupt_manager import save_interrupt_audio
        
        print("\n🧪 Testing with None audio data...")
        result = save_interrupt_audio(None, 'test_session_none')
        print(f"📁 Result with None data: {result}")
        
        if result is None:
            print("✅ SUCCESS: Function correctly returned None for None data")
            return True
        else:
            print("❌ ERROR: Function should return None for None data")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing Fixed Audio Save Functionality")
    print("=" * 50)
    
    # Test 1: Normal audio data
    success1 = test_audio_save_function()
    
    # Test 2: Empty audio data
    success2 = test_empty_audio_data()
    
    # Test 3: None audio data
    success3 = test_none_audio_data()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Normal audio data: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Empty audio data:  {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"   None audio data:   {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if all([success1, success2, success3]):
        print("\n🎉 ALL TESTS PASSED! Audio saving functionality is working correctly.")
    else:
        print("\n⚠️  SOME TESTS FAILED! Check the errors above.")
